<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pages FlexoDiv - Index des Ressources IA</title>
    <link rel="icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        html, body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
            overflow-y: scroll;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE et Edge */
        }
        html::-webkit-scrollbar, body::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
        .gradient-text {
            background: linear-gradient(to right, #4f46e5, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .card {
            background-color: rgba(31, 41, 55, 0.5);
            border: 1px solid rgba(55, 65, 81, 0.7);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: #4f46e5;
        }
        .card-content {
            flex-grow: 1;
        }
        .icon-wrapper {
            background: linear-gradient(135deg, #4f46e5, #ec4899);
            border-radius: 0.75rem;
            padding: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="max-w-6xl mx-auto p-4 sm:p-8">

        <!-- En-tête -->
        <header class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl font-extrabold text-white mb-2 leading-tight">
                Index des<br>
                <span class="gradient-text">Ressources IA</span>
            </h1>
            <p class="text-lg text-gray-400 mt-4">Accédez à toutes les ressources et guides FlexoDiv pour l'intelligence artificielle</p>
        </header>

        <!-- Grille des ressources -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

            <!-- Orchestration de Projets IA -->
            <div class="card">
                <div class="card-content">
                    <div class="icon-wrapper">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-3">
                        <span class="gradient-text">Orchestration</span> de Projets IA
                    </h3>
                    <p class="text-gray-300 mb-4">
                        34 projets d'intelligence artificielle orchestrés avec les meilleurs modèles LLM. Découvrez comment structurer et développer des solutions IA complexes.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs rounded-full">34 Projets</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs rounded-full">Modèles LLM</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white text-xs rounded-full">Architecture</span>
                    </div>
                    <a href="Orchestration_de_Projets_IA.html" class="inline-flex items-center text-indigo-400 hover:text-pink-400 transition-colors duration-300 font-semibold">
                        Découvrir les projets
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Guide OpenRouter Gratuit -->
            <div class="card">
                <div class="card-content">
                    <div class="icon-wrapper">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-3">
                        Guide <span class="gradient-text">OpenRouter</span> Gratuit
                    </h3>
                    <p class="text-gray-300 mb-4">
                        Apprenez à utiliser OpenRouter gratuitement pour accéder aux meilleurs modèles d'IA. Guide complet avec exemples pratiques et astuces.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-gradient-to-r from-green-500 to-blue-600 text-white text-xs rounded-full">Gratuit</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs rounded-full">OpenRouter</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs rounded-full">Guide</span>
                    </div>
                    <a href="Guide-open-router-free.html" class="inline-flex items-center text-indigo-400 hover:text-pink-400 transition-colors duration-300 font-semibold">
                        Lire le guide
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Guide OpenRouter Payant -->
            <div class="card">
                <div class="card-content">
                    <div class="icon-wrapper">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-3">
                        Guide <span class="gradient-text">OpenRouter</span> Premium
                    </h3>
                    <p class="text-gray-300 mb-4">
                        Maximisez votre utilisation d'OpenRouter avec les modèles premium. Stratégies avancées, optimisation des coûts et modèles exclusifs.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-2 py-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white text-xs rounded-full">Premium</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs rounded-full">OpenRouter</span>
                        <span class="px-2 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs rounded-full">Avancé</span>
                    </div>
                    <a href="Guide-openrouter-Paid.html" class="inline-flex items-center text-indigo-400 hover:text-pink-400 transition-colors duration-300 font-semibold">
                        Découvrir le premium
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>

        </div>

        <!-- Section statistiques -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold gradient-text mb-2">34+</div>
                <div class="text-gray-400">Projets IA Orchestrés</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold gradient-text mb-2">100+</div>
                <div class="text-gray-400">Modèles LLM Référencés</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold gradient-text mb-2">3</div>
                <div class="text-gray-400">Guides Complets</div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-16 pt-8 border-t border-gray-700 text-center">
            <div class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-8">
                <p class="text-gray-400">
                    Ressources créées par <span class="gradient-text font-semibold">FlexoDiv IA</span>
                </p>
                <div class="flex flex-wrap justify-center space-x-4 space-y-2">
                    <a href="../../index.html" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🏠 Revenir à l'accueil</a>
                    <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🚀 OpenRouter</a>
                    <a href="../index.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Portfolio</a>
                    <a href="https://github.com/cisco-03/Flexodiv" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">GitHub</a>
                    <a href="https://www.linkedin.com/in/flexodiv-développeur-de-solutions-ia-982582203/" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">LinkedIn</a>
                </div>
            </div>
            <p class="text-gray-500 text-sm mt-4">© 2025 FlexoDiv - Portfolio de développement IA</p>
        </footer>

    </div>

</body>
</html>
