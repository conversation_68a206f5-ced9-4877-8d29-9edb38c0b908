<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guide Stratégique GitHub Copilot Pro Plus - Version Finale Complète</title>
    <link rel="icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        html, body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
            overflow-y: scroll; /* Permet le scroll vertical */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE et Edge */
        }
        html::-webkit-scrollbar, body::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
        .gradient-text {
            background: linear-gradient(to right, #4f46e5, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .card {
            background-color: rgba(31, 41, 55, 0.5);
            border: 1px solid rgba(55, 65, 81, 0.7);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: #4f46e5;
        }
        .card-content {
            flex-grow: 1;
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="max-w-6xl mx-auto p-4 sm:p-8">

        <!-- En-tête -->
        <header class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl font-extrabold text-white mb-2 leading-tight">
                Guide Stratégique<br>
                <span class="gradient-text">GitHub Copilot Pro Plus</span>
            </h1>
            <p class="text-lg text-gray-400 mt-4">Le guide complet pour optimiser l'usage et les coûts de chaque modèle IA.</p>
        </header>

        <!-- Légende des couleurs -->
        <div class="bg-gray-800/50 rounded-xl p-6 mb-12 border border-gray-700">
            <h3 class="font-bold text-xl text-white mb-4">Légende des Coûts</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="flex items-center gap-3">
                    <span class="font-bold text-indigo-400">In:</span>
                    <p class="text-gray-300">Coût pour le prompt envoyé (Entrée).</p>
                </div>
                <div class="flex items-center gap-3">
                    <span class="font-bold text-pink-400">Out:</span>
                    <p class="text-gray-300">Coût pour la réponse générée (Sortie).</p>
                </div>
                 <div class="flex items-center gap-3">
                    <span class="font-bold text-teal-400">Unique:</span>
                    <p class="text-gray-300">Un seul coût pour l'entrée et la sortie.</p>
                </div>
            </div>
            <p class="text-xs text-gray-500 mt-4 text-center">Note : Les coûts des multiplicateurs sont basés sur la tarification publique de GitHub, exprimée en dollars américains ($).</p>
        </div>

        <!-- Tier Spécial : Inclus dans l'abonnement -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-6 text-teal-400">⭐ Tier Spécial : Inclus dans l'Abonnement</h2>
            <p class="text-center text-gray-400 mb-8 max-w-3xl mx-auto">Ces modèles ont un multiplicateur de <span class="font-bold text-white">0</span>. Ils sont le cœur de ton workflow et n'utilisent pas tes crédits premium. Abuse-en !</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="card border-teal-500/50">
                    <div class="card-content">
                        <h3 class="text-xl font-bold text-white">OpenAI GPT-4o</h3>
                        <p class="text-gray-400 text-sm mt-2">Le couteau suisse ultime. Parfait pour la discussion technique, la génération de code complexe et le brainstorming créatif.</p>
                    </div>
                </div>
                <div class="card border-teal-500/50">
                    <div class="card-content">
                        <h3 class="text-xl font-bold text-white">OpenAI GPT-4.1</h3>
                        <p class="text-gray-400 text-sm mt-2">Une alternative puissante à GPT-4o, idéale pour obtenir une seconde perspective sur un problème complexe ou une architecture logicielle.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tiers de Coûts -->
        <div class="space-y-16">

            <!-- Tier 1: Usage Quotidien -->
            <div>
                <h2 class="text-3xl font-bold text-center mb-4">🥇 Tier 1 : Sprinters Économiques</h2>
                <p class="text-center text-gray-500 text-xl mb-8">(Coût Ultra-Faible)</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-4-mini-instruct</h3><p class="text-gray-400 text-sm mt-2">Le plus rentable du marché. Idéal pour l'autocomplétion en continu et les tâches de scripting simples.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.0075</span> / <span class="text-pink-400">Out: 0.03</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-4-multimodal-instruct</h3><p class="text-gray-400 text-sm mt-2">Le choix économique pour analyser des images de code ou des maquettes simples.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.008</span> / <span class="text-pink-400">Out: 0.032</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-4</h3><p class="text-gray-400 text-sm mt-2">Un excellent modèle de base, rapide et efficace pour la génération de code standard.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.0125</span> / <span class="text-pink-400">Out: 0.05</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3.5-mini instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">Très polyvalent et peu coûteux pour des contextes larges. Bon pour résumer des fichiers.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.013</span> / <span class="text-pink-400">Out: 0.052</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3.5-vision instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">Une bonne entrée en matière pour l'analyse d'UI ou de schémas à partir d'images.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.013</span> / <span class="text-pink-400">Out: 0.052</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-mini instruct (4k)</h3><p class="text-gray-400 text-sm mt-2">Parfait pour les prompts courts et les réponses rapides. Très agile.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.013</span> / <span class="text-pink-400">Out: 0.052</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-mini instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">La version à large contexte du mini, pour analyser des documents entiers à faible coût.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.013</span> / <span class="text-pink-400">Out: 0.052</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-small instruct (8k)</h3><p class="text-gray-400 text-sm mt-2">Un bon équilibre entre la taille du contexte et la performance pour les tâches quotidiennes.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.015</span> / <span class="text-pink-400">Out: 0.06</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-small instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">Idéal pour des analyses de code base ou de documentation volumineuse.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.015</span> / <span class="text-pink-400">Out: 0.06</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">OpenAI GPT-4o mini</h3><p class="text-gray-400 text-sm mt-2">La puissance de GPT-4o dans un format plus rapide et moins cher. Excellent pour le dialogue et le code.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.015</span> / <span class="text-pink-400">Out: 0.06</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3.5-MoE instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">Modèle "Mixture of Experts", efficace pour des tâches variées tout en restant abordable.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.016</span> / <span class="text-pink-400">Out: 0.064</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-medium instruct (4k)</h3><p class="text-gray-400 text-sm mt-2">Une montée en gamme pour des générations plus complexes, avec un contexte court.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.017</span> / <span class="text-pink-400">Out: 0.068</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Phi-3-medium instruct (128k)</h3><p class="text-gray-400 text-sm mt-2">La version la plus puissante de la famille Phi-3, pour des tâches complexes sur de grands documents.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.017</span> / <span class="text-pink-400">Out: 0.068</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Grok 3 Mini</h3><p class="text-gray-400 text-sm mt-2">Pour des réponses rapides, directes et parfois originales. Bon pour débloquer une situation.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.025</span> / <span class="text-pink-400">Out: 0.127</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Llama 4 Maverick 17B Instruct FP8</h3><p class="text-gray-400 text-sm mt-2">Un excellent modèle open-source, performant et customisable, à un coût très compétitif.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.025</span> / <span class="text-pink-400">Out: 0.1</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">OpenAI GPT-4.1-mini</h3><p class="text-gray-400 text-sm mt-2">Une version plus légère du GPT-4.1, utile quand la vitesse prime sur la profondeur d'analyse.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.04</span> / <span class="text-pink-400">Out: 0.16</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Llama-3.3-70B-Instruct</h3><p class="text-gray-400 text-sm mt-2">Le grand frère de la famille Llama, très puissant pour le code et le raisonnement, avec un coût symétrique.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.071</span> / <span class="text-pink-400">Out: 0.071</span></p></div>
                </div>
            </div>

            <!-- Tier 2: Outils Polyvalents -->
            <div>
                <h2 class="text-3xl font-bold text-center mb-4">🥈 Tier 2 : Polyvalents Équilibrés</h2>
                 <p class="text-center text-gray-500 text-xl mb-8">(Coût Modéré)</p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">DeepSeek-V3-0324</h3><p class="text-gray-400 text-sm mt-2">Spécialisé dans le code, ce modèle est un expert pour comprendre et générer des logiques complexes.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.114</span> / <span class="text-pink-400">Out: 0.456</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">DeepSeek-R1</h3><p class="text-gray-400 text-sm mt-2">Un spécialiste du code. À utiliser pour le refactoring ou la création d'algorithmes.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.135</span> / <span class="text-pink-400">Out: 0.54</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">DeepSeek-R1-0528</h3><p class="text-gray-400 text-sm mt-2">Une version plus récente de DeepSeek, potentiellement avec de meilleures performances en codage.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.135</span> / <span class="text-pink-400">Out: 0.54</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">MAI-DS-R1</h3><p class="text-gray-400 text-sm mt-2">Une autre variante de la famille DeepSeek, à comparer pour des cas d'usage de code spécifiques.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.135</span> / <span class="text-pink-400">Out: 0.54</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Gemini 2.0 Flash</h3><p class="text-gray-400 text-sm mt-2">Le modèle rapide de Google. Excellent pour le brainstorming et la génération rapide d'idées ou de snippets.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 0.25</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Claude 4 mini ("o4-mini")</h3><p class="text-gray-400 text-sm mt-2">Un bon compromis de la famille Claude, plus abordable que les versions complètes.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 0.33</span></p></div>
                </div>
            </div>

            <!-- Tier 3: Spécialistes & Experts -->
            <div>
                <h2 class="text-3xl font-bold text-center mb-4">🥉 Tier 3 : Experts Puissants</h2>
                <p class="text-center text-gray-500 text-xl mb-8">(Coût Élevé)</p>
                 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Grok 3</h3><p class="text-gray-400 text-sm mt-2">Le modèle le plus puissant de Grok. Pour les problèmes très complexes, mais attention à la sortie coûteuse.</p></div><p class="font-mono text-xs mt-4"><span class="text-indigo-400">In: 0.3</span> / <span class="text-pink-400">Out: 1.5</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Claude 3 Opus ("o3")</h3><p class="text-gray-400 text-sm mt-2">Un des meilleurs modèles pour le raisonnement et le suivi d'instructions complexes. Fiable et robuste.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 1.0</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Claude Sonnet 3.5 / 3.7 / 4</h3><p class="text-gray-400 text-sm mt-2">Le cœur de la gamme Claude. Excellent équilibre entre performance et coût pour des tâches exigeantes.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 1.0</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Gemini 2.5 Pro</h3><p class="text-gray-400 text-sm mt-2">Le modèle Pro de Google. Très performant pour comprendre de larges bases de code et proposer des solutions architecturales.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 1.0</span></p></div>
                    <div class="card"><div class="card-content"><h3 class="font-semibold text-white">Claude Sonnet 3.7 Thinking</h3><p class="text-gray-400 text-sm mt-2">Une variante spécialisée pour le raisonnement. Idéale pour le débogage et l'analyse logique de problèmes.</p></div><p class="font-mono text-xs mt-4"><span class="text-teal-400">Unique: 1.25</span></p></div>
                 </div>
            </div>
            
            <!-- Tier 4: Artillerie Lourde -->
            <div>
                <h2 class="text-3xl font-bold text-center mb-4 text-red-500">🎖️ Tier 4 : Le Titan</h2>
                <p class="text-center text-gray-500 text-xl mb-8">(Coût Extrême)</p>
                <div class="max-w-md mx-auto">
                    <div class="card border-red-500/50">
                        <div class="card-content">
                            <h3 class="font-semibold text-white">Claude Opus 4</h3>
                            <p class="text-gray-400 text-sm mt-2">L'arme ultime pour le raisonnement complexe, la R&D et les problèmes qui semblent insolubles. À utiliser avec une extrême parcimonie.</p>
                        </div>
                        <p class="font-mono text-lg mt-4"><span class="text-teal-400">Unique: 10.0</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Astuces Stratégiques -->
        <div class="mt-20">
            <h2 class="text-3xl font-bold text-center mb-12">💡 Astuces Stratégiques : Le Workflow d'un Projet</h2>
            <div class="space-y-10">
                <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
                    <h3 class="text-xl font-bold text-white mb-3">1. Démarrage du Projet : Brainstorming & Initialisation</h3>
                    <p class="text-gray-400 mb-4">L'objectif est d'explorer les idées et de mettre en place la structure de base rapidement et à moindre coût.</p>
                    <p class="text-gray-300"><strong class="text-teal-400">Combinaison Suggérée :</strong> <span class="font-semibold">GPT-4o (Inclus)</span> pour le dialogue, le brainstorming et la définition de l'architecture. Complétez avec un <span class="font-semibold">Phi-4-mini-instruct</span> pour générer le boilerplate, les fichiers de configuration et les scripts de base.</p>
                </div>
                <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
                    <h3 class="text-xl font-bold text-white mb-3">2. Phase de Développement : Logique Métier & Fonctionnalités</h3>
                    <p class="text-gray-400 mb-4">Ici, on a besoin de puissance pour implémenter la logique complexe tout en gardant un œil sur les coûts.</p>
                    <p class="text-gray-300"><strong class="text-teal-400">Combinaison Suggérée :</strong> Utilisez massivement <span class="font-semibold">GPT-4o / 4.1 (Inclus)</span> pour le gros du travail. Si vous êtes bloqué ou si vous avez besoin d'une autre perspective sur une fonction complexe, faites appel à <span class="font-semibold">Claude Sonnet 3.5</span> ou <span class="font-semibold">Gemini 2.5 Pro</span> pour une analyse ponctuelle.</p>
                </div>
                <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
                    <h3 class="text-xl font-bold text-white mb-3">3. Quand ça se Corse : Débogage Complexe & R&D</h3>
                    <p class="text-gray-400 mb-4">Face à un bug vicieux, un problème d'architecture ou une preuve de concept innovante, le coût devient secondaire par rapport à la puissance de raisonnement.</p>
                    <p class="text-gray-300"><strong class="text-teal-400">Combinaison Suggérée :</strong> C'est le moment de sortir l'artillerie lourde. <span class="font-semibold text-red-400">Claude Opus 4 (Le Titan)</span> est votre meilleur atout pour le raisonnement pur. <span class="font-semibold">Grok 3</span> peut aussi offrir des solutions inattendues, mais attention à son coût de sortie élevé.</p>
                </div>
                <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
                    <h3 class="text-xl font-bold text-white mb-3">4. Fin de Projet : Refactoring, Optimisation & Documentation</h3>
                    <p class="text-gray-400 mb-4">Cette phase mélange des tâches répétitives et des analyses profondes.</p>
                    <p class="text-gray-300"><strong class="text-teal-400">Combinaison Suggérée :</strong> Pour la documentation (docstrings, README), utilisez un modèle économique comme <span class="font-semibold">Phi-4-mini-instruct</span>. Pour le refactoring et l'optimisation du code, revenez à <span class="font-semibold">GPT-4o (Inclus)</span> ou <span class="font-semibold">Claude Sonnet 3.7 Thinking</span> pour une analyse fine et des suggestions pertinentes.</p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-20 pt-10 border-t border-gray-800">
            <!-- Navigation Links -->
            <div class="mb-8">
                <div class="flex flex-wrap justify-center space-x-4 space-y-2 mb-6">
                    <a href="../../index.html" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🏠 Accueil</a>
                    <a href="index.html" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">📋 Revenir à l'index</a>
                    <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🚀 OpenRouter</a>
                    <a href="Guide-openrouter-free.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Guide Gratuit</a>
                    <a href="Guide-openrouter-Paid.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Guide Payant</a>
                    <a href="Orchestration_de_Projets_IA.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Orchestration IA</a>
                </div>
            </div>

            <!-- Social Links -->
            <p class="text-gray-400 mb-6">Retrouvez-moi sur :</p>
            <div class="flex justify-center items-center space-x-8">
                <a href="https://flexodiv.netlify.app" target="_blank" rel="noopener noreferrer" title="FlexoDiv">
                    <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" class="h-8 w-auto hover:opacity-80 transition-opacity">
                </a>
                <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" title="Contact par email">
                    <img src="https://i.imgur.com/bd2iJdM.png" alt="Logo Gmail" class="h-8 w-8 hover:opacity-80 transition-opacity">
                </a>
                <a href="https://www.linkedin.com/in/flexodiv-developpeur-de-solutions-ia-982582203/" target="_blank" rel="noopener noreferrer" title="Profil LinkedIn">
                    <img src="https://i.imgur.com/CLcVMuK.png" alt="Logo LinkedIn" class="h-8 w-8 hover:opacity-80 transition-opacity">
                </a>
                <a href="https://www.youtube.com/@flexodiv" target="_blank" rel="noopener noreferrer" title="Chaîne YouTube">
                    <img src="https://i.imgur.com/xdm9nE7.png" alt="Logo YouTube" class="h-8 w-8 hover:opacity-80 transition-opacity">
                </a>
            </div>
            <p class="text-gray-500 text-sm mt-4">© 2025 FlexoDiv - Portfolio de développement IA</p>
        </footer>

    </div>
</body>
</html>
