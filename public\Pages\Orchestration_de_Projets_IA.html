<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orchestration de Projets IA - Portfolio FlexoDiv</title>
    <link rel="icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        html, body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
            overflow-y: scroll;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE et Edge */
        }
        html::-webkit-scrollbar, body::-webkit-scrollbar {
            width: 0px;
            background: transparent;
        }
        .gradient-text {
            background: linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .client-title {
            background: linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        .card {
            background-color: rgba(31, 41, 55, 0.5);
            border: 1px solid rgba(55, 65, 81, 0.7);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-bottom: 1.5rem;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: #4f46e5;
        }
        .card-content {
            flex-grow: 1;
        }
        .phase {
            margin-bottom: 12px;
            padding: 12px;
            background: rgba(55, 65, 81, 0.3);
            border-radius: 8px;
            border-left: 3px solid #4f46e5;
        }
        .tool_link {
            color: #4f46e5;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .tool_link:hover {
            color: #ec4899;
            text-decoration: underline;
        }
        .request {
            background: rgba(55, 65, 81, 0.3);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            border-left: 4px solid #4f46e5;
        }
    </style>
</head>
<body class="text-gray-200">

    <div class="mx-auto px-5 sm:px-8 py-4 sm:py-8" style="max-width: calc(100vw - 40px);">

        <!-- En-tête -->
        <header class="text-center mb-12">
            <h1 class="text-4xl sm:text-5xl font-extrabold text-white mb-2 leading-tight">
                Orchestration de<br>
                <span class="gradient-text">Projets IA</span>
            </h1>
            <p class="text-lg text-gray-400 mt-4">34 projets d'intelligence artificielle orchestrés avec les meilleurs modèles LLM</p>

            <!-- Description explicative -->
            <div class="max-w-4xl mx-auto mt-8 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                <p class="text-gray-300 text-center leading-relaxed">
                    Cette section présente des <strong class="text-blue-400">demandes réelles de clients</strong> avec des exemples détaillés de
                    <strong class="text-purple-400">plans d'action structurés</strong>. Chaque projet utilise des
                    <strong class="gradient-text">modèles LLM spécialisés</strong> adaptés à chaque phase de développement,
                    démontrant une approche méthodique de l'orchestration de projets d'intelligence artificielle complexes.
                </p>
            </div>
        </header>

        <!-- Grille des projets -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

            <!-- Client 1: Le Fonds Quantitatif HFT -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 1: Le Fonds Quantitatif HFT</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système de trading HFT pour cryptomonnaies, analysant les marchés et les news en temps réel pour prendre des décisions en microsecondes."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Découverte d'Alpha & Stratégie:</strong><br>
                            <span class="text-gray-300">Analyse de données historiques pour identifier des signaux de trading.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Pipeline d'Ingestion de Données (Rust):</strong><br>
                            <span class="text-gray-300">Écriture des connecteurs ultra-rapides pour les flux WebSocket.</span><br>
                            <a href="https://openrouter.ai/mistralai/codestral-2508" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/codestral-2508]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Moteur d'Analyse de Sentiment en Temps Réel:</strong><br>
                            <span class="text-gray-300">Développement d'un classificateur de sentiment spécialisé.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Cœur de la Logique de Trading (C++):</strong><br>
                            <span class="text-gray-300">Implémentation de la stratégie pour une latence minimale.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-coder-33b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-coder-33b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Simulateur de Backtesting Historique:</strong><br>
                            <span class="text-gray-300">Création d'un environnement de simulation pour valider la stratégie.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Module de Gestion des Risques:</strong><br>
                            <span class="text-gray-300">Conception d'un "kill switch" et de modules de sécurité.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Dashboard de Monitoring (WebAssembly):</strong><br>
                            <span class="text-gray-300">Création d'une interface web en temps réel sans impacter la latence.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Audit de Sécurité et Piste de Conformité:</strong><br>
                            <span class="text-gray-300">Génération de scripts pour l'audit et la conformité réglementaire.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r-plus]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 2: L'Institut de Radiologie Avancée -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 2: L'Institut de Radiologie Avancée</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système d'aide au diagnostic qui ingère des images DICOM, détecte des anomalies et génère un pré-rapport en langage naturel pour les radiologues."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Anonymisation et Conformité HIPAA:</strong><br>
                            <span class="text-gray-300">Création d'un script Python pour garantir la confidentialité des données.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Pipeline de Prétraitement d'Images:</strong><br>
                            <span class="text-gray-300">Normalisation et préparation des images pour l'analyse.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Modèle de Détection d'Anomalies (PyTorch):</strong><br>
                            <span class="text-gray-300">Fine-tuning d'un modèle de vision pour la classification et la segmentation.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Algorithme de Comparaison Temporelle:</strong><br>
                            <span class="text-gray-300">Conception d'un algorithme pour comparer les scans dans le temps.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Génération du Pré-Rapport Structuré:</strong><br>
                            <span class="text-gray-300">Combinaison des résultats de l'analyse et des données patient.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Rédaction en Langage Naturel Médical:</strong><br>
                            <span class="text-gray-300">Transformation du rapport structuré en un texte fluide et précis.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Interface de Visualisation (Web Viewer DICOM):</strong><br>
                            <span class="text-gray-300">Développement d'un visualiseur web interactif pour le radiologue.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Système de Feedback et d'Amélioration Continue:</strong><br>
                            <span class="text-gray-300">Création de l'interface de validation pour améliorer le modèle.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 3: Le Géant de la Logistique Mondiale -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 3: Le Géant de la Logistique Mondiale</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Digital Twin' de notre chaîne d'approvisionnement mondiale pour simuler, prédire les retards et suggérer des re-routages optimaux."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Modélisation Ontologique:</strong><br>
                            <span class="text-gray-300">Définir tous les objets de la simulation et leurs relations.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Intégration des API de Données Temps Réel:</strong><br>
                            <span class="text-gray-300">Écriture des connecteurs pour les services météo, trafic, etc.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Cœur du Moteur de Simulation:</strong><br>
                            <span class="text-gray-300">Développement du modèle de simulation basé sur des événements.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Modèle Prédictif de Perturbations:</strong><br>
                            <span class="text-gray-300">Entraînement d'un modèle pour prédire la probabilité d'un retard.</span><br>
                            <a href="https://openrouter.ai/tencent/hunyuan-a13b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[tencent/hunyuan-a13b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Agent d'Optimisation par Renforcement:</strong><br>
                            <span class="text-gray-300">Conception de l'agent qui apprend la politique de re-routage optimale.</span><br>
                            <a href="https://openrouter.ai/thedrummer/valkyrie-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[thedrummer/valkyrie-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Moteur de Calcul des Coûts:</strong><br>
                            <span class="text-gray-300">Implémentation de la logique complexe des coûts (carburant, taxes...).</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-medium-3" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-medium-3]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Visualisation 3D du "Digital Twin":</strong><br>
                            <span class="text-gray-300">Création d'un tableau de bord avec une carte 3D temps réel.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Générateur de Scénarios de Crise:</strong><br>
                            <span class="text-gray-300">Création d'un module pour simuler des crises et voir les solutions de l'IA.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3-mini" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3-mini]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 4: L'Agence de Cyber-Défense Nationale -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 4: L'Agence de Cyber-Défense Nationale</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système de 'chasse aux menaces' autonome qui analyse les logs réseau, détecte les anomalies et génère des hypothèses d'attaque en utilisant le framework MITRE ATT&CK."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Pipeline d'Ingestion de Logs à Grande Échelle:</strong><br>
                            <span class="text-gray-300">Mise en place d'une architecture de données (Kafka, Spark) pour traiter des térabytes de logs.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-14b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-14b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Modélisation du Comportement Normal:</strong><br>
                            <span class="text-gray-300">Utilisation de techniques non-supervisées pour créer un modèle du trafic "normal".</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-small" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-small]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Moteur de Détection d'Anomalies:</strong><br>
                            <span class="text-gray-300">Comparaison du trafic en temps réel au modèle de baseline pour lever des alertes.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Enrichissement des Alertes:</strong><br>
                            <span class="text-gray-300">Ajout automatique de contexte à chaque alerte (géolocalisation IP, etc.).</span><br>
                            <a href="https://openrouter.ai/arcee-ai/spotlight" class="tool_link" target="_blank" rel="noopener noreferrer">[arcee-ai/spotlight]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Corrélation et Formation de "Clusters" d'Attaque:</strong><br>
                            <span class="text-gray-300">Regroupement des alertes individuelles en un "incident" potentiel.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-8b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-8b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Génération d'Hypothèses d'Attaque (Raisonnement):</strong><br>
                            <span class="text-gray-300">Mapping des incidents aux tactiques et techniques du framework MITRE ATT&CK.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Automatisation des Investigations (SOAR):</strong><br>
                            <span class="text-gray-300">Lancement automatique d'actions de base pour l'investigation.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-4-scout" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-4-scout]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Rapport pour l'Analyste Humain:</strong><br>
                            <span class="text-gray-300">Génération d'un rapport complet avec une visualisation de la "kill chain".</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 5: La Banque d'Investissement Internationale -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 5: La Banque d'Investissement Internationale</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système de détection de fraude sur carte de crédit en temps réel, bloquant les transactions suspectes en moins de 50ms et fournissant une explication."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception:</strong><br>
                            <span class="text-gray-300">Définir les types de fraude et les contraintes de latence.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-medium-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-medium-2506]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture:</strong><br>
                            <span class="text-gray-300">Concevoir une architecture de streaming avec Kafka et Go.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Moteur de Features):</strong><br>
                            <span class="text-gray-300">Écrire en Python/Spark le pipeline de calcul des features client.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Modèle ML):</strong><br>
                            <span class="text-gray-300">Entraîner un modèle XGBoost sur des données historiques.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur de Décision Go):</strong><br>
                            <span class="text-gray-300">Implémenter le modèle en Go pour une inférence ultra-rapide.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Test:</strong><br>
                            <span class="text-gray-300">Créer un simulateur pour tester la latence et la précision.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-coder-33b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-coder-33b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement:</strong><br>
                            <span class="text-gray-300">Écrire les manifestes Kubernetes pour un déploiement redondant.</span><br>
                            <a href="https://openrouter.ai/microsoft/phi-4-reasoning-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[microsoft/phi-4-reasoning-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Maintenance (Explicabilité):</strong><br>
                            <span class="text-gray-300">Développer un dashboard qui explique les raisons du blocage.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r-plus]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 6: La Mairie d'une Mégalopole -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 6: La Mairie d'une Mégalopole</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système de régulation adaptative des feux de circulation qui analyse le trafic en temps réel pour ajuster dynamiquement la durée des feux et fluidifier le trafic."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Architecture:</strong><br>
                            <span class="text-gray-300">Concevoir une architecture IoT avec traitement en "edge computing".</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Edge AI):</strong><br>
                            <span class="text-gray-300">Écrire en C++/TensorFlow Lite le modèle de vision pour compter les véhicules.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Jumeau Numérique):</strong><br>
                            <span class="text-gray-300">Créer une simulation de la ville dans le cloud (avec SUMO).</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (IA Centrale):</strong><br>
                            <span class="text-gray-300">Entraîner un agent par renforcement pour apprendre la politique de gestion.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Intégration:</strong><br>
                            <span class="text-gray-300">Développer l'API sécurisée entre l'IA centrale et les feux.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Déploiement:</strong><br>
                            <span class="text-gray-300">Orchestrer le déploiement des modèles sur la flotte de boîtiers IoT.</span><br>
                            <a href="https://openrouter.ai/mistralai/codestral-2508" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/codestral-2508]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Maintenance:</strong><br>
                            <span class="text-gray-300">Créer un dashboard de supervision pour les ingénieurs trafic.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash-lite" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash-lite]</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client 7: Le Studio d'Animation Révolutionnaire -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 7: Le Studio d'Animation Révolutionnaire</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une suite logicielle permettant à un seul artiste de produire un court-métrage d'animation à partir d'un script et de croquis, en générant assets, animation et voix."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception:</strong><br>
                            <span class="text-gray-300">Définir le pipeline de production de l'idée à la vidéo finale.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Analyse de Script):</strong><br>
                            <span class="text-gray-300">Le système lit le script et identifie scènes, personnages, émotions.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-small-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-small-2506]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Génération d'Assets):</strong><br>
                            <span class="text-gray-300">Un modèle génératif crée les modèles 3D à partir des croquis.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-coder-33b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-coder-33b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Moteur d'Animation):</strong><br>
                            <span class="text-gray-300">Un modèle multimodal génère l'animation brute dans Blender.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Génération Vocale):</strong><br>
                            <span class="text-gray-300">Un modèle de Text-to-Speech cloné génère les dialogues.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Intégration (Plugin Blender):</strong><br>
                            <span class="text-gray-300">Empaqueter la suite dans un plugin pour Blender.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Post-Production:</strong><br>
                            <span class="text-gray-300">Créer des outils assistés par IA pour le montage et l'étalonnage.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-235b-a22b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-235b-a22b]</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client 8: Le Géant Pharmaceutique -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 8: Le Géant Pharmaceutique</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une plateforme IA pour accélérer la découverte de médicaments, ingérant la recherche, prédisant le repliement des protéines et proposant des molécules candidates."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception & Cadrage:</strong><br>
                            <span class="text-gray-300">Définir le domaine thérapeutique et les critères de succès.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture:</strong><br>
                            <span class="text-gray-300">Concevoir une architecture de données pour informations hétérogènes.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Ingestion de Connaissances):</strong><br>
                            <span class="text-gray-300">Crawler et structurer les informations de PubMed et PubChem.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Analyse de Protéines):</strong><br>
                            <span class="text-gray-300">Intégrer des modèles type AlphaFold pour prédire la structure 3D.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Génération de Molécules):</strong><br>
                            <span class="text-gray-300">Créer un modèle génératif qui propose de nouvelles structures moléculaires.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Test & Validation (In Silico):</strong><br>
                            <span class="text-gray-300">Développer un module de simulation de "docking" moléculaire.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement (Interface Chercheur):</strong><br>
                            <span class="text-gray-300">Créer une interface web pour visualiser les protéines et les simulations.</span><br>
                            <a href="https://openrouter.ai/arcee-ai/spotlight" class="tool_link" target="_blank" rel="noopener noreferrer">[arcee-ai/spotlight]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Maintenance:</strong><br>
                            <span class="text-gray-300">Mettre en place un système de MLOps pour ré-entraîner les modèles.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-14b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-14b]</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client 9: L'Éducateur du Futur -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 9: L'Éducateur du Futur</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une plateforme d'apprentissage K-12 entièrement adaptative qui diagnostique les faiblesses d'un élève et génère un parcours d'apprentissage personnalisé."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception:</strong><br>
                            <span class="text-gray-300">Modéliser le "graphe de connaissances" d'une matière.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture:</strong><br>
                            <span class="text-gray-300">Concevoir l'architecture tripartite (élève, enseignant, moteur IA).</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-235b-a22b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-235b-a22b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Moteur d'Évaluation):</strong><br>
                            <span class="text-gray-300">Créer des quiz dynamiques pour identifier la racine des difficultés.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-medium" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-medium]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Générateur de Contenu):</strong><br>
                            <span class="text-gray-300">Générer des explications et des exercices sur mesure.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur d'Adaptation):</strong><br>
                            <span class="text-gray-300">Implémenter l'algorithme qui choisit la prochaine brique de contenu.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Test:</strong><br>
                            <span class="text-gray-300">Créer des agents "élèves synthétiques" pour simuler l'utilisation.</span><br>
                            <a href="https://openrouter.ai/microsoft/phi-4-reasoning-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[microsoft/phi-4-reasoning-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement (Front-End):</strong><br>
                            <span class="text-gray-300">Construire les applications web gamifiées et le tableau de bord.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Maintenance:</strong><br>
                            <span class="text-gray-300">Permettre aux enseignants de visualiser et d'ajuster les parcours.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3-mini" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3-mini]</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client 10: Le Cabinet d'Avocats International -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 10: Le Cabinet d'Avocats International</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Co-pilote Juridique' pour analyser des contrats, identifier les clauses à risque en les comparant à une base de données, et suggérer des réécritures."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception:</strong><br>
                            <span class="text-gray-300">Définir une taxonomie des clauses juridiques et une matrice de risques.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture:</strong><br>
                            <span class="text-gray-300">Concevoir un pipeline de traitement de documents hautement sécurisé.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Ingestion & Structuration):</strong><br>
                            <span class="text-gray-300">Extraire et segmenter le texte de PDF scannés complexes.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-14b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-14b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Classification Sémantique):</strong><br>
                            <span class="text-gray-300">Fine-tuner un modèle pour reconnaître et classifier chaque clause.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Analyse d'Écarts):</strong><br>
                            <span class="text-gray-300">Comparer chaque clause à la clause standard et calculer un "score de déviance".</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Explication des Risques):</strong><br>
                            <span class="text-gray-300">Générer une explication en langage clair des risques juridiques.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-small-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-small-2506]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Développement (Suggestion de Réécriture):</strong><br>
                            <span class="text-gray-300">Proposer des alternatives de réécriture conformes.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Intégration:</strong><br>
                            <span class="text-gray-300">Empaqueter l'outil sous forme d'un Add-in pour Microsoft Word.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 11: Le Consortium AgriTech -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 11: Le Consortium AgriTech</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une plateforme de 'Precision Farming' qui agrège données satellite et capteurs IoT pour fournir des recommandations quotidiennes (irrigation, engrais)."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Architecture :</strong><br>
                            <span class="text-gray-300">Concevoir une architecture de données géo-spatiales et temporelles.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash-lite" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash-lite]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Ingestion Géo-spatiale) :</strong><br>
                            <span class="text-gray-300">Écrire les scripts pour traiter les images satellite et calculer le NDVI.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-medium-3" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-medium-3]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Ingestion IoT) :</strong><br>
                            <span class="text-gray-300">Construire le backend pour recevoir les données des capteurs de terrain.</span><br>
                            <a href="https://openrouter.ai/tencent/hunyuan-a13b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[tencent/hunyuan-a13b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Modèle Prédictif) :</strong><br>
                            <span class="text-gray-300">Entraîner un modèle pour prédire le rendement et le stress hydrique.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-4-scout" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-4-scout]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur de Recommandation) :</strong><br>
                            <span class="text-gray-300">Créer un système expert qui transforme les prédictions en conseils.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Déploiement (Application Mobile) :</strong><br>
                            <span class="text-gray-300">Développer une application mobile simple et robuste.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Maintenance (Boucle de Feedback) :</strong><br>
                            <span class="text-gray-300">Enregistrer les actions et les rendements pour améliorer les modèles.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 12: L'Agence de Gestion des Catastrophes -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 12: L'Agence de Gestion des Catastrophes</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Common Operational Picture' pour la gestion de crise, fusionnant en temps réel les signalements des réseaux sociaux, l'imagerie satellite et la position des secours."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Architecture :</strong><br>
                            <span class="text-gray-300">Concevoir une architecture hautement résiliente et disponible.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-small" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-small]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Analyse des Réseaux Sociaux) :</strong><br>
                            <span class="text-gray-300">Filtrer, géolocaliser et classifier l'urgence des messages.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Analyse Géo-spatiale Différentielle) :</strong><br>
                            <span class="text-gray-300">Comparer les images satellite pour détecter les infrastructures détruites.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Fusion de Données) :</strong><br>
                            <span class="text-gray-300">Fusionner les couches d'information sur une carte dynamique.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-8b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-8b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Aide à la Décision) :</strong><br>
                            <span class="text-gray-300">Recommander l'allocation des ressources en fonction des besoins.</span><br>
                            <a href="https://openrouter.ai/thedrummer/valkyrie-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[thedrummer/valkyrie-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Déploiement (Interface Unifiée) :</strong><br>
                            <span class="text-gray-300">Créer l'interface cartographique pour le centre de commandement.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test :</strong><br>
                            <span class="text-gray-300">Mettre en place des exercices de simulation à grande échelle.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 13: Le Studio de Création Musicale -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 13: Le Studio de Création Musicale</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Collaborateur Musical IA' qui, à partir d'une mélodie fredonnée et d'instructions de style, génère une orchestration MIDI complète."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception :</strong><br>
                            <span class="text-gray-300">Modéliser la théorie musicale et les caractéristiques stylistiques.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-medium" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-medium]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Transcription Audio) :</strong><br>
                            <span class="text-gray-300">Transcrire l'enregistrement audio de la mélodie en une partition MIDI.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Analyse de Style) :</strong><br>
                            <span class="text-gray-300">Analyser les instructions et charger les paramètres stylistiques.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Génération Harmonie & Rythme) :</strong><br>
                            <span class="text-gray-300">Générer la progression d'accords, la basse et la batterie.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Orchestration & Contrepoint) :</strong><br>
                            <span class="text-gray-300">Ajouter les autres couches instrumentales et le contrepoint.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Déploiement (Export MIDI & Intégration DAW) :</strong><br>
                            <span class="text-gray-300">Exporter le résultat en MIDI et l'intégrer dans les logiciels de musique.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 14: Le Réseau Social Décentralisé -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 14: Le Réseau Social Décentralisé</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une plateforme sociale décentralisée avec un système de modération ouvert où les utilisateurs choisissent leurs propres 'filtres de modération' IA."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Architecture :</strong><br>
                            <span class="text-gray-300">Concevoir le protocole décentralisé et le système d'identité.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Cœur du Protocole) :</strong><br>
                            <span class="text-gray-300">Implémenter le nœud du réseau en Go ou Rust.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Modèles de Modération de Base) :</strong><br>
                            <span class="text-gray-300">Entraîner des modèles experts pour la toxicité, le spam, etc.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-4-scout" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-4-scout]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Marché des Filtres) :</strong><br>
                            <span class="text-gray-300">Créer un système pour que des tiers soumettent leurs modèles.</span><br>
                            <a href="https://openrouter.ai/arcee-ai/spotlight" class="tool_link" target="_blank" rel="noopener noreferrer">[arcee-ai/spotlight]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur de Filtrage Côté Client) :</strong><br>
                            <span class="text-gray-300">Appliquer la modération directement sur l'appareil de l'utilisateur.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e2b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e2b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Déploiement (Application de Référence) :</strong><br>
                            <span class="text-gray-300">Construire une application web et mobile open-source.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3-mini" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3-mini]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Maintenance (Gouvernance) :</strong><br>
                            <span class="text-gray-300">Concevoir les outils d'une gouvernance décentralisée (DAO).</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-14b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-14b]</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client 15: L'Alliance Géopolitique & Financière -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 15: L'Alliance Géopolitique & Financière</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Global Sentinel' IA qui anticipe les chocs de marché en analysant des données non-structurées (rumeurs, imagerie satellite, câbles diplomatiques) et construit des graphes de causalité."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception & Cadrage Éthique :</strong><br>
                            <span class="text-gray-300">Définir les événements à surveiller et établir un cadre éthique.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-medium-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-medium-2506]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture de Données 'Firehose' :</strong><br>
                            <span class="text-gray-300">Concevoir une architecture pour ingérer des flux de données hétérogènes.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Agents Collecteurs Spécialisés) :</strong><br>
                            <span class="text-gray-300">Créer des agents IA autonomes pour extraire des informations.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Moteur de Détection de Relations) :</strong><br>
                            <span class="text-gray-300">Détecter les relations subtiles entre les entités.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Graphe de Causalité Dynamique) :</strong><br>
                            <span class="text-gray-300">Modéliser les liens de cause à effet en temps réel.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Moteur de Simulation & Scénarios) :</strong><br>
                            <span class="text-gray-300">Lancer des simulations pour générer des scénarios futurs.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test (Red Teaming) :</strong><br>
                            <span class="text-gray-300">Tenter de tromper le système avec de fausses informations.</span><br>
                            <a href="https://openrouter.ai/microsoft/phi-4-reasoning-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[microsoft/phi-4-reasoning-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Dashboard Analyste) :</strong><br>
                            <span class="text-gray-300">Créer une interface pour naviguer dans le graphe de causalité.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Maintenance (Apprentissage par le Feedback) :</strong><br>
                            <span class="text-gray-300">Comparer les prédictions aux événements réels pour affiner le système.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 16: L'Initiative de Chirurgie Télé-Autonome -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 16: L'Initiative de Chirurgie Télé-Autonome</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Le logiciel pour un robot chirurgical avec retour haptique et un 'co-pilote' IA qui stabilise les gestes et peut agir en cas d'urgence."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception & Sécurité Critique :</strong><br>
                            <span class="text-gray-300">Définir le cadre de sécurité (norme ISO 13485).</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture Réseau Ultra-Faible Latence :</strong><br>
                            <span class="text-gray-300">Concevoir un protocole de compression vidéo et haptique sur mesure.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-coder-33b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-coder-33b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Jumeau Numérique du Patient) :</strong><br>
                            <span class="text-gray-300">Créer un modèle 3D du patient pour l'entraînement.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Interface Haptique) :</strong><br>
                            <span class="text-gray-300">Traduire la résistance des tissus en retour de force.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (IA de Perception) :</strong><br>
                            <span class="text-gray-300">Identifier en temps réel les tissus, vaisseaux et instruments.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-235b-a22b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-235b-a22b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (IA Co-Pilote - Assistance) :</strong><br>
                            <span class="text-gray-300">Filtrer les tremblements et créer des "zones d'interdiction".</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Développement (IA Co-Pilote - Action d'Urgence) :</strong><br>
                            <span class="text-gray-300">Entraîner un agent à exécuter des actions d'urgence.</span><br>
                            <a href="https://openrouter.ai/mistralai/codestral-2508" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/codestral-2508]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Test & Validation Exhaustifs :</strong><br>
                            <span class="text-gray-300">Créer une suite de tests avec des milliers de scénarios simulés.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Déploiement (Boîte Noire) :</strong><br>
                            <span class="text-gray-300">Enregistrer toutes les données dans une "boîte noire" inviolable.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 10 - Maintenance & Mise à Jour :</strong><br>
                            <span class="text-gray-300">Utiliser les données de chaque opération pour affiner les modèles.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 17: L'Opérateur du Réseau Électrique National -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 17: L'Opérateur du Réseau Électrique National</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Une IA pour gérer notre réseau électrique, prédisant consommation et production, optimisant les flux, anticipant les pannes et se défendant contre les cyberattaques."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception :</strong><br>
                            <span class="text-gray-300">Modéliser l'ensemble du réseau électrique sous forme de graphe.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Développement (Modèles Prédictifs) :</strong><br>
                            <span class="text-gray-300">Prédire la production renouvelable et la consommation.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Moteur d'Optimisation) :</strong><br>
                            <span class="text-gray-300">Recalculer la distribution d'énergie la plus stable et la moins chère.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-235b-a22b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-235b-a22b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Maintenance Prédictive) :</strong><br>
                            <span class="text-gray-300">Prédire les pannes d'équipement avant qu'elles ne surviennent.</span><br>
                            <a href="https://openrouter.ai/arcee-ai/spotlight" class="tool_link" target="_blank" rel="noopener noreferrer">[arcee-ai/spotlight]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Cyber-Défense Active) :</strong><br>
                            <span class="text-gray-300">Détecter et isoler de manière autonome une cyberattaque.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Test (Jumeau Numérique de Crise) :</strong><br>
                            <span class="text-gray-300">Tester la réaction du système à des scénarios extrêmes.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement (Interface de Supervision) :</strong><br>
                            <span class="text-gray-300">Créer un centre de contrôle pour les opérateurs humains.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash-lite" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash-lite]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Edge) :</strong><br>
                            <span class="text-gray-300">Déployer des modèles d'IA plus petits dans les sous-stations.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Gouvernance :</strong><br>
                            <span class="text-gray-300">Rédiger les rapports de conformité pour les régulateurs.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 18: Le Consortium de Restauration d'Art -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 18: Le Consortium de Restauration d'Art</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système pour assister la restauration de peintures, qui analyse les pigments, et prédit les couleurs et coups de pinceau manquants dans le style de l'artiste original."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception :</strong><br>
                            <span class="text-gray-300">Créer une base de données des techniques et matériaux des grands maîtres.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-medium" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-medium]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture :</strong><br>
                            <span class="text-gray-300">Concevoir le pipeline de traitement pour images gigapixels.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Analyse Chimique) :</strong><br>
                            <span class="text-gray-300">Reconnaître la signature spectrale des pigments historiques.</span><br>
                            <a href="https://openrouter.ai/tencent/hunyuan-a13b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[tencent/hunyuan-a13b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Analyse Stylistique) :</strong><br>
                            <span class="text-gray-300">Fine-tuner un modèle pour qu'il apprenne le style de l'artiste.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur d'Inpainting) :</strong><br>
                            <span class="text-gray-300">Un modèle génératif remplit les zones endommagées.</span><br>
                            <a href="https://openrouter.ai/thedrummer/valkyrie-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[thedrummer/valkyrie-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Test (Validation par l'Expert) :</strong><br>
                            <span class="text-gray-300">L'IA génère plusieurs propositions, l'expert humain choisit.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-small" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-small]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement (Projection en RA) :</strong><br>
                            <span class="text-gray-300">Projeceter la restauration virtuelle sur l'œuvre réelle.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Traçabilité :</strong><br>
                            <span class="text-gray-300">Enregistrer chaque intervention dans un registre immuable.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3-mini" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3-mini]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Partage des Connaissances :</strong><br>
                            <span class="text-gray-300">Générer des articles de recherche sur les techniques découvertes.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-small-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-small-2506]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 19: L'Agence Spatiale Privée 'Odyssey' -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 19: L'Agence Spatiale Privée 'Odyssey'</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Directeur de Mission IA' pour une colonie sur Mars, gérant support de vie, production, planification de missions, et santé mentale de l'équipage."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception (Modèle Psychologique) :</strong><br>
                            <span class="text-gray-300">Modéliser les facteurs de stress et de cohésion d'un groupe isolé.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-medium" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-medium]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture (OS de la Base) :</strong><br>
                            <span class="text-gray-300">Concevoir un OS temps réel, tolérant aux pannes et aux radiations.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Optimisation des Ressources) :</strong><br>
                            <span class="text-gray-300">Un agent par renforcement pour allouer énergie, eau et nutriments.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Planificateur Scientifique) :</strong><br>
                            <span class="text-gray-300">Planifier les trajets des rovers pour maximiser les découvertes.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Diagnostic Médical) :</strong><br>
                            <span class="text-gray-300">Analyser les signes vitaux pour détecter les problèmes de santé.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-4-scout" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-4-scout]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Compagnon IA) :</strong><br>
                            <span class="text-gray-300">Un confident IA personnel pour chaque membre d'équipage.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Développement (Gestion de l'Environnement) :</strong><br>
                            <span class="text-gray-300">Ajuster lumières et musique pour améliorer le moral.</span><br>
                            <a href="https://openrouter.ai/x-ai/grok-3-mini" class="tool_link" target="_blank" rel="noopener noreferrer">[x-ai/grok-3-mini]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Test (Simulation Humaine Intégrale) :</strong><br>
                            <span class="text-gray-300">Simuler l'équipage pour tester le système sur des années virtuelles.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Éthique & Contrôle :</strong><br>
                            <span class="text-gray-300">Permettre à l'équipage d'ignorer les recommandations de l'IA.</span><br>
                            <a href="https://openrouter.ai/arcee-ai/spotlight" class="tool_link" target="_blank" rel="noopener noreferrer">[arcee-ai/spotlight]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 10 - Communication avec la Terre :</strong><br>
                            <span class="text-gray-300">Compresser les rapports de mission en un paquet de données optimisé.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 20: Le Consortium pour la Préservation Historique -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 20: Le Consortium pour la Préservation Historique</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Reconstruire numériquement une langue morte (ex: Sumérien) en déchiffrant des textes, en construisant un modèle grammatical, et en permettant une conversation basique."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception & Cadrage Linguistique :</strong><br>
                            <span class="text-gray-300">Définir les principes de la langue et les objectifs.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture (Base de Données Polyglotte) :</strong><br>
                            <span class="text-gray-300">Concevoir une BDD pour écritures non-standard.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (OCR Cunéiforme) :</strong><br>
                            <span class="text-gray-300">Entraîner un modèle de vision pour transcrire les tablettes.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-3-8b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-3-8b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Modèle de Langue Masqué) :</strong><br>
                            <span class="text-gray-300">Entraîner un LLM sur le corpus pour qu'il apprenne la grammaire.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Traduction Statistique & Neuronale) :</strong><br>
                            <span class="text-gray-300">Construire un modèle de traduction pour déduire le sens des mots.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-14b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-14b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Générateur de Grammaire Formelle) :</strong><br>
                            <span class="text-gray-300">Expliciter les règles de grammaire pour les humains.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e2b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e2b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test (Génération & Validation par Experts) :</strong><br>
                            <span class="text-gray-300">Générer de nouvelles phrases et les faire valider par les experts.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Portail de Recherche) :</strong><br>
                            <span class="text-gray-300">Créer un portail web pour explorer le corpus et converser.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Préservation :</strong><br>
                            <span class="text-gray-300">Archiver le modèle de langue et la BDD dans un format standardisé.</span><br>
                            <a href="https://openrouter.ai/meta-llama/llama-4-scout" class="tool_link" target="_blank" rel="noopener noreferrer">[meta-llama/llama-4-scout]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 21: Le Fond d'Investissement d'Impact Social -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 21: Le Fond d'Investissement d'Impact Social</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Mesurer l'impact réel de projets sociaux en Afrique via des données alternatives (satellite, radios locales) et des simulations contrefactuelles."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception (Théorie du Changement) :</strong><br>
                            <span class="text-gray-300">Modéliser la chaîne causale attendue pour chaque projet.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture :</strong><br>
                            <span class="text-gray-300">Intégrer des données socio-économiques, géospatiales et qualitatives.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Analyse Géo-spatiale) :</strong><br>
                            <span class="text-gray-300">Mesurer des proxys de développement via images satellite.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-8b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-8b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Analyse Audio) :</strong><br>
                            <span class="text-gray-300">Analyser les radios locales pour mesurer le sentiment communautaire.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Modèle Économétrique Contrefactuel) :</strong><br>
                            <span class="text-gray-300">Estimer l'impact causal net de l'investissement.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-sonnet-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-sonnet-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Synthèse de Rapports Qualitatifs) :</strong><br>
                            <span class="text-gray-300">Extraire les thèmes des rapports de terrain.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Déploiement (Dashboard d'Impact) :</strong><br>
                            <span class="text-gray-300">Présenter une vue holistique de l'impact.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-flash-lite" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-flash-lite]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Maintenance (Rapport Annuel Automatisé) :</strong><br>
                            <span class="text-gray-300">Générer automatiquement le rapport d'impact annuel.</span><br>
                            <a href="https://openrouter.ai/mistralai/magistral-small-2506" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/magistral-small-2506]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 22: Le Studio de Jeux Vidéo 'Hyperion' -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 22: Le Studio de Jeux Vidéo 'Hyperion'</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un 'Directeur de Jeu IA' pour un MMORPG qui crée dynamiquement des arcs narratifs, réagit à l'économie et organise des événements mondiaux."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception (Modèle Narratif):</strong><br>
                            <span class="text-gray-300">Créer une "grammaire d'histoire" pour l'IA.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.3-nemotron-super-49b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture (Cerveau du Monde):</strong><br>
                            <span class="text-gray-300">Permettre à l'IA de lire/écrire l'état du jeu en temps réel.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Analyse Économique):</strong><br>
                            <span class="text-gray-300">Surveiller l'économie du jeu, détecter inflation et manipulation.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Générateur de Quêtes Contextuelles):</strong><br>
                            <span class="text-gray-300">Générer des quêtes uniques basées sur les actions des joueurs.</span><br>
                            <a href="https://openrouter.ai/microsoft/phi-4-reasoning-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[microsoft/phi-4-reasoning-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Moteur d'Événements Mondiaux):</strong><br>
                            <span class="text-gray-300">Déclencher des événements pour maintenir l'équilibre du monde.</span><br>
                            <a href="https://openrouter.ai/cohere/command-r-plus" class="tool_link" target="_blank" rel="noopener noreferrer">[cohere/command-r-plus]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (PNJ Émergents):</strong><br>
                            <span class="text-gray-300">Générer le dialogue des PNJ en temps réel.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test (Simulation de Communauté):</strong><br>
                            <span class="text-gray-300">Tester le Directeur IA avec des milliers de joueurs simulés.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Outils de Supervision Humaine):</strong><br>
                            <span class="text-gray-300">Un dashboard pour que les GMs humains supervisent l'IA.</span><br>
                            <a href="https://openrouter.ai/openai/gpt-4.1" class="tool_link" target="_blank" rel="noopener noreferrer">[openai/gpt-4.1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 9 - Maintenance:</strong><br>
                            <span class="text-gray-300">L'IA apprend des réactions des joueurs pour s'améliorer.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 23: Le Fournisseur de Solutions de Santé Mentale -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 23: Le Fournisseur de Solutions de Santé Mentale</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un thérapeute IA spécialisé en TCC, menant des sessions par texte, proposant des exercices, et détectant les signes de détresse aiguë pour escalader à un humain."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception & Éthique:</strong><br>
                            <span class="text-gray-300">Modéliser les protocoles de thérapie avec des psychologues.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-coder-33b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-coder-33b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture Sécurisée et Confidentielle:</strong><br>
                            <span class="text-gray-300">Concevoir une architecture de données "zero-knowledge".</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Moteur Conversationnel TCC):</strong><br>
                            <span class="text-gray-300">Fine-tuner un modèle sur des transcriptions de sessions TCC.</span><br>
                            <a href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview" class="tool_link" target="_blank" rel="noopener noreferrer">[nousresearch/deephermes-3-mistral-24b-preview]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Analyse des Distorsions Cognitives):</strong><br>
                            <span class="text-gray-300">Reconnaître en temps réel les distorsions cognitives.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Générateur d'Exercices):</strong><br>
                            <span class="text-gray-300">Générer et expliquer des exercices de TCC personnalisés.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-32b" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-32b]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Détecteur de Crise - 'Red Flag'):</strong><br>
                            <span class="text-gray-300">Un modèle de sécurité distinct pour détecter les risques suicidaires.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test (Simulation de Patients):</strong><br>
                            <span class="text-gray-300">Tester la robustesse et la sécurité avec des agents IA.</span><br>
                            <a href="https://openrouter.ai/mistralai/devstral-medium" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/devstral-medium]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Supervision par des Professionnels):</strong><br>
                            <span class="text-gray-300">Un portail pour que des thérapeutes humains auditent l'IA.</span><br>
                            <a href="https://openrouter.ai/mistralai/codestral-2508" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/codestral-2508]</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client 24: L'Entreprise de Bio-Ingénierie 'Genesis' -->
            <div class="card">
                <div class="card-content">
                    <h3 class="text-xl font-bold mb-3">
                        <span class="client-title">Client 24: L'Entreprise de Bio-Ingénierie 'Genesis'</span>
                    </h3>
                    <div class="request text-gray-300 italic mb-4">
                        <strong class="text-indigo-400">Demande:</strong> "Un système d'IA pour concevoir des organismes synthétiques, comme une bactérie qui se nourrit de plastique et produit un biocarburant, en simulant son métabolisme avant sa synthèse."
                    </div>
                    <h4 class="text-lg font-semibold text-indigo-400 mb-3">Plan d'Action</h4>
                    <div class="space-y-3">
                        <div class="phase">
                            <strong class="text-white">Phase 1 - Conception (Définition de la Fonction Métabolique):</strong><br>
                            <span class="text-gray-300">Définir l'objectif biologique précis.</span><br>
                            <a href="https://openrouter.ai/google/gemma-3n-e4b-it" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemma-3n-e4b-it]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 2 - Architecture (Base de Données Génomiques):</strong><br>
                            <span class="text-gray-300">Créer une BDD de gènes connus et des protéines qu'ils codent.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 3 - Développement (Concepteur de Voies Métaboliques):</strong><br>
                            <span class="text-gray-300">Construire une nouvelle voie métabolique à partir de gènes existants.</span><br>
                            <a href="https://openrouter.ai/deepseek/deepseek-r1-0528" class="tool_link" target="_blank" rel="noopener noreferrer">[deepseek/deepseek-r1-0528]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 4 - Développement (Générateur de Génome):</strong><br>
                            <span class="text-gray-300">Écrire la séquence d'ADN complète du génome synthétique.</span><br>
                            <a href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct" class="tool_link" target="_blank" rel="noopener noreferrer">[mistralai/mistral-small-3.2-24b-instruct]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 5 - Développement (Simulateur de Pliage de Protéines):</strong><br>
                            <span class="text-gray-300">Prédire la structure 3D des nouvelles protéines.</span><br>
                            <a href="https://openrouter.ai/anthropic/claude-opus-4" class="tool_link" target="_blank" rel="noopener noreferrer">[anthropic/claude-opus-4]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 6 - Développement (Simulateur Métabolique Holistique):</strong><br>
                            <span class="text-gray-300">Simuler la cellule virtuelle pour prédire son comportement.</span><br>
                            <a href="https://openrouter.ai/google/gemini-2.5-pro" class="tool_link" target="_blank" rel="noopener noreferrer">[google/gemini-2.5-pro]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 7 - Test (Analyse des Risques d'Évasion):</strong><br>
                            <span class="text-gray-300">Analyser le génome pour des risques de biosécurité.</span><br>
                            <a href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1" class="tool_link" target="_blank" rel="noopener noreferrer">[nvidia/llama-3.1-nemotron-ultra-253b-v1]</a>
                        </div>
                        <div class="phase">
                            <strong class="text-white">Phase 8 - Déploiement (Génération de Protocole de Synthèse):</strong><br>
                            <span class="text-gray-300">Générer le protocole de laboratoire pour synthétiser l'organisme.</span><br>
                            <a href="https://openrouter.ai/qwen/qwen3-coder" class="tool_link" target="_blank" rel="noopener noreferrer">[qwen/qwen3-coder]</a>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

        <!-- Footer -->
        <footer class="mt-16 pt-8 border-t border-gray-700 text-center">
            <div class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-8">
                <p class="text-gray-400">
                    Orchestration générée par <span class="gradient-text font-semibold">FlexoDiv IA</span>
                </p>
                <div class="flex flex-wrap justify-center space-x-4 space-y-2">
                    <a href="../../index.html" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🏠 Accueil</a>
                    <a href="../index.html" target="_blank" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">📋 Index Pages</a>
                    <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">🚀 OpenRouter</a>
                    <a href="../index.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Accueil</a>
                    <a href="Guide-openrouter-Paid.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Guide OpenRouter</a>
                    <a href="modeles-llm.html" class="text-indigo-400 hover:text-pink-400 transition-colors duration-300">Modèles LLM</a>
                </div>
            </div>
            <p class="text-gray-500 text-sm mt-4">© 2025 FlexoDiv - Portfolio de développement IA</p>
        </footer>

    </div>

</body>
</html>